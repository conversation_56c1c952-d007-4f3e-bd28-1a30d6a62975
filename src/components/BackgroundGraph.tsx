"use client";
import { useEffect, useRef } from 'react';

interface DataPoint {
  x: number;
  y: number;
}

export default function BackgroundGraph() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const dataPointsRef = useRef<DataPoint[]>([]);
  const offsetRef = useRef(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    let width: number, height: number;
    const maxPoints = 200;
    const stepX = 5;

    function resizeCanvas() {
      if (!canvas) return;
      width = canvas.width = window.innerWidth;
      height = canvas.height = window.innerHeight;
    }

    function updateData() {
      const dataPoints = dataPointsRef.current;
      if (dataPoints.length >= maxPoints) {
        dataPoints.shift();
      }
      const lastY = dataPoints.length > 0 ? dataPoints[dataPoints.length - 1].y : height / 2;
      const nextY = lastY + (Math.random() - 0.5) * 100;
      dataPoints.push({ x: offsetRef.current, y: Math.max(0, Math.min(height, nextY)) });
      offsetRef.current += stepX;
    }

    function drawGraph() {
      if (!ctx || !canvas) return;

      ctx.clearRect(0, 0, width, height);
      ctx.beginPath();
      ctx.lineWidth = 4;
      // Using a slightly darker blue than rgb(43, 114, 214)
      ctx.strokeStyle = '#1e5bb8'; // This is darker than #2b72d6

      const dataPoints = dataPointsRef.current;

      // Calculate how many points we need to fill the screen width
      const pointsNeeded = Math.ceil(width / stepX) + 1;

      // If we don't have enough points, generate more to fill the screen
      while (dataPoints.length < pointsNeeded) {
        const lastY = dataPoints.length > 0 ? dataPoints[dataPoints.length - 1].y : height / 2;
        const nextY = lastY + (Math.random() - 0.5) * 100;
        dataPoints.push({ x: offsetRef.current, y: Math.max(0, Math.min(height, nextY)) });
        offsetRef.current += stepX;
      }

      for (let i = 0; i < dataPoints.length; i++) {
        const point = dataPoints[i];
        const x = i * stepX;
        const y = point.y;

        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          const jitterX = x + (Math.random() - 0.5) * 9;
          const jitterY = y + (Math.random() - 0.5) * 4;
          ctx.lineTo(jitterX, jitterY);
        }
      }
      ctx.stroke();
    }

    function animate() {
      updateData();
      drawGraph();
      animationRef.current = setTimeout(() => {
        requestAnimationFrame(animate);
      }, 100);
    }

    // Initialize
    window.addEventListener('resize', resizeCanvas);
    resizeCanvas();
    animate();

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (animationRef.current) {
        clearTimeout(animationRef.current);
      }
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className="background-graph"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100vw',
        height: '100vh',
        zIndex: -1,
      }}
    />
  );
}
